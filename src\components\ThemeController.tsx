'use client'

import { useEffect, useRef } from 'react'
import { useThemeStore, type Theme } from '@/stores'

interface ThemeOption {
  value: Theme
  label: string
  icon: string
}

const themeOptions: ThemeOption[] = [
  {
    value: 'light',
    label: 'Light',
    icon: 'icon-[solar--sun-fog-bold-duotone]',
  },
  {
    value: 'dark',
    label: 'Dark',
    icon: 'icon-[solar--moon-stars-bold-duotone]',
  },
  {
    value: 'system',
    label: 'System',
    icon: 'icon-[solar--monitor-bold-duotone]',
  },
]

interface ThemeControllerProps {
  className?: string
  buttonClassName?: string
  dropdownClassName?: string
}

export function ThemeController({
  className = '',
  buttonClassName = '',
  dropdownClassName = '',
}: ThemeControllerProps) {
  const { theme, resolvedTheme, setTheme, initializeTheme } = useThemeStore()
  const dropdownRef = useRef<HTMLDivElement>(null)
  const buttonRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    initializeTheme()
  }, [initializeTheme])

  const handleThemeSelect = (selectedTheme: Theme) => {
    setTheme(selectedTheme)
    if (buttonRef.current) {
      buttonRef.current.blur()
    }
  }

  const getCurrentIcon = () => {
    if (theme === 'system') {
      const isDark = resolvedTheme === 'dark'
      return isDark
        ? 'icon-[solar--moon-stars-bold-duotone] text-violet-400'
        : 'icon-[solar--sun-fog-bold-duotone] text-amber-500'
    }

    const option = themeOptions.find((option) => option.value === theme)
    if (!option) return themeOptions[0].icon

    const colorClass =
      option.value === 'dark' ? 'text-violet-400' : option.value === 'light' ? 'text-amber-500' : ''
    return `${option.icon} ${colorClass}`.trim()
  }

  return (
    <div className={`dropdown dropdown-end ${className}`} ref={dropdownRef}>
      <div
        ref={buttonRef}
        tabIndex={0}
        role="button"
        className={`btn btn-circle btn-outline border-base-300 ${buttonClassName}`}
        aria-label={`Current theme: ${theme}`}
        aria-haspopup="menu"
        aria-expanded="false"
      >
        <span className={getCurrentIcon()} aria-hidden="true"></span>
      </div>

      <ul
        tabIndex={0}
        className={`dropdown-content menu gap-1 bg-base-100 rounded-box z-[1] w-40 shadow-md border border-base-300 ${dropdownClassName}`}
        role="menu"
        aria-label="Theme selection menu"
      >
        {themeOptions.map((option) => (
          <li key={option.value} role="none">
            <button
              type="button"
              className={`flex items-center gap-2 px-3 py-2 hover:bg-base-200 ${
                theme === option.value ? 'bg-primary text-primary-content hover:bg-primary' : ''
              }`}
              onClick={() => handleThemeSelect(option.value)}
              role="menuitem"
              aria-label={option.label}
            >
              <span className={option.icon} aria-hidden="true"></span>
              <div className="flex flex-col items-start">
                <span className="font-medium">{option.label}</span>
              </div>
              {theme === option.value && (
                <span className="icon-[solar--check-circle-bold] ml-auto" aria-hidden="true"></span>
              )}
            </button>
          </li>
        ))}
      </ul>
    </div>
  )
}
