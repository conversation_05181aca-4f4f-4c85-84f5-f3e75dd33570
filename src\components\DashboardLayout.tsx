'use client'

import { Sidebar, Navbar } from '@/components'

export function DashboardLayout({ children }: { children?: React.ReactNode }) {
  return (
    <div className="flex h-screen m-4">
      <div className="drawer lg:drawer-open lg:gap-4">
        <Sidebar />
        <div className="drawer-content">
          <div className="grid grid-rows-[auto_1fr]">
            <Navbar />
            <main className="min-h-0 overflow-auto" role="main" aria-label="Main content">
              {children}
            </main>
          </div>
        </div>
      </div>
    </div>
  )
}
