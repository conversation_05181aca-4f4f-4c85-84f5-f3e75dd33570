import Image from 'next/image'
import Link from 'next/link'

export function UserProfile() {
  return (
    <div className="dropdown dropdown-bottom dropdown-end">
      <div tabIndex={0} role="button" className="btn btn-ghost px-1.5">
        <div className="flex items-center gap-2">
          <div className="avatar">
            <div className="bg-base-200 mask mask-circle w-8">
              <Image src="/1.png" alt="Avatar" width={32} height={32} />
            </div>
          </div>
          <div className="text-start">
            <p className="text-sm">Username</p>
            <p className="text-xs text-base-content/80">Role</p>
          </div>
        </div>
      </div>
      <div tabIndex={0} className="dropdown-content bg-base-100 rounded-box mt-4 w-40 shadow">
        <ul className="menu w-full p-2">
          <li>
            <Link href="/user/profile">
              <span className="icon-[solar--user-rounded-bold-duotone]"></span>
              <span className="font-medium text-sm">My Account</span>
            </Link>
          </li>
          <li>
            <Link href="/user/settings">
              <span className="icon-[solar--settings-bold-duotone]"></span>
              <span className="font-medium text-sm">Settings</span>
            </Link>
          </li>
          <li>
            <Link href="/help">
              <span className="icon-[solar--question-circle-bold-duotone]"></span>
              <span className="font-medium text-sm">Help</span>
            </Link>
          </li>
        </ul>
        <hr className="border-base-300" />
        <ul className="menu w-full p-2">
          <li>
            <Link href="/logout" className="hover:bg-error/10 text-error">
              <span className="icon-[solar--logout-bold-duotone]"></span>
              <span className="font-medium text-sm">Log out</span>
            </Link>
          </li>
        </ul>
      </div>
    </div>
  )
}
